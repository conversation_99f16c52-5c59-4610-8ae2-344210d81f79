[project]
name = "jupymcp"
version = "0.2.0"
description = "Jupyter + MCP with comprehensive resource support"
readme = "README.md"
authors = [
    { name = "<PERSON><PERSON> (唐梓涯)", email = "<EMAIL>" },
]
requires-python = ">=3.11"
dependencies = [
    "jupyter-collaboration>=4.1.1",
    "jupyter-kernel-client>=0.8.0",
    "jupyter-nbmodel-client>=0.14.0",
    "jupyterlab>=4.4.6",
    "mcp>=1.13.1",
]
[project.scripts]
jupymcp = "jupymcp:main"

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"
