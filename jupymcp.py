import argparse
import asyncio
import logging
import subprocess
import time
import sys
import os
import atexit
import json
import re
import urllib.request
import urllib.error
from pathlib import Path

from jupyter_kernel_client import KernelClient
from jupyter_nbmodel_client import (
    NbModelClient,
    get_jupyter_notebook_websocket_url,
)
from mcp.server.fastmcp import FastMCP

mcp = FastMCP("jupyter")

logger = logging.getLogger(__name__)

kernel: KernelClient
notebook: NbModelClient

# Global variable to track the started server process
_jupyter_server_process = None


def _cleanup_jupyter_server():
    """Clean up the Jupyter server process on exit."""
    global _jupyter_server_process
    if _jupyter_server_process and _jupyter_server_process.poll() is None:
        logger.info("Shutting down Jupyter server...")
        _jupyter_server_process.terminate()
        try:
            _jupyter_server_process.wait(timeout=5)
        except subprocess.TimeoutExpired:
            _jupyter_server_process.kill()
            _jupyter_server_process.wait()


def _wait_for_server_ready(
    server_url: str, token: str, max_wait_time: int = 30
) -> bool:
    """
    Wait for the Jupyter server to be ready to accept connections.

    Args:
        server_url: The server URL to check
        token: The server token for authentication
        max_wait_time: Maximum time to wait in seconds

    Returns:
        bool: True if server is ready, False if timeout
    """
    logger.info(f"Waiting for server to be ready at {server_url}...")
    start_time = time.time()

    while time.time() - start_time < max_wait_time:
        try:
            # Try to make a simple request to the server
            url = f"{server_url}/api/status?token={token}"
            request = urllib.request.Request(url)
            with urllib.request.urlopen(request, timeout=5) as response:
                if response.status == 200:
                    logger.info("Server is ready to accept connections")
                    return True
        except (urllib.error.URLError, urllib.error.HTTPError, OSError):
            # Server not ready yet, wait a bit
            pass

        time.sleep(1)

    logger.warning(f"Server did not become ready within {max_wait_time} seconds")
    return False


def _start_jupyter_server(notebook_path: Path) -> tuple[str, str]:
    """
    Start a Jupyter notebook server and return the server URL and token.

    Args:
        notebook_path: Path to the notebook file to open

    Returns:
        tuple: (server_url, token)

    Raises:
        RuntimeError: If unable to start the server or extract connection info
    """
    global _jupyter_server_process

    # Register cleanup function
    atexit.register(_cleanup_jupyter_server)

    # Create the notebook file if it doesn't exist
    if not notebook_path.exists():
        notebook_path.parent.mkdir(parents=True, exist_ok=True)
        # Create a minimal notebook structure
        notebook_content = {
            "cells": [],
            "metadata": {
                "kernelspec": {
                    "display_name": "Python 3",
                    "language": "python",
                    "name": "python3",
                },
                "language_info": {"name": "python", "version": sys.version},
            },
            "nbformat": 4,
            "nbformat_minor": 4,
        }
        notebook_path.write_text(json.dumps(notebook_content, indent=2))
        logger.info(f"Created new notebook: {notebook_path}")

    # Start Jupyter server
    cmd = ["jupyter", "lab", "--no-browser", f"--notebook-dir={notebook_path.parent}"]

    logger.info(f"Starting Jupyter server with command: {' '.join(cmd)}")

    try:
        _jupyter_server_process = subprocess.Popen(
            cmd,
            stdout=subprocess.PIPE,
            stderr=subprocess.STDOUT,  # Redirect stderr to stdout to capture all output
            text=True,
            bufsize=1,
            universal_newlines=True,
        )

        # Parse server output to extract URL and token
        server_url = None
        token = None
        max_wait_time = 30  # Maximum time to wait for server startup
        start_time = time.time()

        logger.info("Waiting for Jupyter server to start and parsing output...")

        while time.time() - start_time < max_wait_time:
            # Check if process is still running
            if _jupyter_server_process.poll() is not None:
                # Process has terminated
                remaining_output = (
                    _jupyter_server_process.stdout.read()
                    if _jupyter_server_process.stdout
                    else ""
                )
                raise RuntimeError(
                    f"Jupyter server failed to start. Output: {remaining_output}"
                )

            # Read output line by line
            line = _jupyter_server_process.stdout.readline()
            if line:
                logger.debug(f"Jupyter output: {line.strip()}")

                # Look for URL with token in the output
                # Pattern matches: http://localhost:8888/?token=<token> or http://127.0.0.1:8888/?token=<token>
                url_pattern = (
                    r"https?://(?:localhost|127\.0\.0\.1):(\d+)/\?token=([a-f0-9]+)"
                )
                match = re.search(url_pattern, line)
                if match:
                    port_found = match.group(1)
                    token = match.group(2)
                    server_url = f"http://localhost:{port_found}"
                    logger.info(f"Found server URL: {server_url}")
                    logger.info(f"Found token: {token}")
                    break

                # Alternative pattern for URLs without query parameters (extract token from separate lines)
                if "http://localhost:" in line or "http://127.0.0.1:" in line:
                    url_match = re.search(
                        r"https?://(localhost|127\.0\.0\.1):(\d+)", line
                    )
                    if url_match:
                        port_found = url_match.group(2)
                        server_url = f"http://localhost:{port_found}"
                        logger.info(f"Found server URL: {server_url}")

                # Look for token in separate lines
                token_pattern = r"token=([a-f0-9]+)"
                token_match = re.search(token_pattern, line)
                if token_match:
                    token = token_match.group(1)
                    logger.info(f"Found token: {token}")

                # Break if we have both URL and token
                if server_url and token:
                    break
            else:
                # No more output available, wait a bit
                time.sleep(0.1)

        if not server_url or not token:
            # Try to get any remaining output
            remaining_output = ""
            try:
                remaining_output = _jupyter_server_process.stdout.read()
            except Exception:
                pass
            raise RuntimeError(
                f"Could not extract server URL and token from Jupyter output. Server URL: {server_url}, Token: {token}. Output: {remaining_output}"
            )

        # Wait for server to be ready to accept connections
        if not _wait_for_server_ready(server_url, token):
            raise RuntimeError(
                f"Jupyter server started but is not ready to accept connections at {server_url}"
            )

        # Give the server a bit more time to fully initialize all services
        logger.info("Server is ready, waiting a moment for full initialization...")
        time.sleep(3)

        logger.info(f"Jupyter server started successfully at {server_url}")
        return server_url, token

    except Exception as e:
        if _jupyter_server_process:
            _jupyter_server_process.terminate()
            _jupyter_server_process = None
        raise RuntimeError(f"Failed to start Jupyter server: {e}")


def get_default_notebook_path() -> Path:
    """Get a default notebook path in the current working directory."""
    i = 0
    while True:
        notebook_path = Path.cwd() / f"Untitled{f' {i}' if i > 0 else ''}.ipynb"
        if not notebook_path.exists():
            return notebook_path
        i += 1


def extract_output(output: dict) -> str:
    """
    Extracts readable output from a Jupyter cell output dictionary.

    Args:
        output (dict): The output dictionary from a Jupyter cell.

    Returns:
        str: A string representation of the output.
    """
    output_type = output.get("output_type")
    match output_type:
        case "stream":
            return output.get("text", "")
    if output_type == "stream":
        return output.get("text", "")
    elif output_type in ["display_data", "execute_result"]:
        data = output.get("data", {})
        if "text/plain" in data:
            return data["text/plain"]
        elif "text/html" in data:
            return "[HTML Output]"
        elif "image/png" in data:
            return "[Image Output (PNG)]"
        else:
            return f"[{output_type} Data: keys={list(data.keys())}]"
    elif output_type == "error":
        return output["traceback"]
    else:
        return f"[Unknown output type: {output_type}]"


@mcp.tool()
async def execute_cell(
    index: int,
) -> list[str]:
    """Execute a notebook cell by index.

    Args:
        index: Cell index to execute

    Returns:
        list[str]: List of outputs from the executed cell
    """
    result = notebook.execute_cell(index, kernel)
    return [extract_output(output) for output in result["outputs"]]


@mcp.tool()
async def add_and_execute_code_cell(source: str) -> list[str]:
    """Add and execute a code cell in a Jupyter notebook.

    Args:
        source: Code content

    Returns:
        list[str]: List of outputs from the executed cell
    """

    index = notebook.add_code_cell(source)
    return await execute_cell(index)


@mcp.tool()
async def set_and_execute_code_cell(index: int, source: str) -> list[str]:
    """Set and execute a code cell in a Jupyter notebook.

    Args:
        index: Cell index to set
        source: New cell source

    Returns:
        list[str]: List of outputs from the executed cell
    """
    notebook.set_cell_source(index, source)
    return await execute_cell(index)


def main():
    # Disable proxy for localhost connections at the start
    original_proxies = os.environ.copy()
    parser = argparse.ArgumentParser(
        description="JupyMCP - Jupyter + MCP with comprehensive resource support"
    )
    parser.add_argument(
        "--server-url",
        help="Jupyter server URL (if not provided, will start a default server)",
    )
    parser.add_argument(
        "--token",
        help="Jupyter server token (if not provided, will start a default server)",
    )
    parser.add_argument(
        "--path",
        type=Path,
        help="Path to notebook file (if not provided, will use/create Untitled.ipynb)",
    )
    args = parser.parse_args()
    try:
        # Clear proxy environment variables for localhost
        for proxy_var in ["HTTP_PROXY", "HTTPS_PROXY", "http_proxy", "https_proxy"]:
            if proxy_var in os.environ:
                del os.environ[proxy_var]

        # Determine server connection parameters
        if args.server_url and args.token:
            # Use provided server
            server_url = args.server_url
            token = args.token
            notebook_path = args.path or get_default_notebook_path()
            logger.info(f"Using existing Jupyter server at {server_url}")
        else:
            # Start default server
            notebook_path = args.path or get_default_notebook_path()
            logger.info("Starting default Jupyter server...")
            try:
                server_url, token = _start_jupyter_server(notebook_path)
            except RuntimeError as e:
                logger.error(f"Failed to start Jupyter server: {e}")
                return 1

        # Convert absolute path to relative path for the websocket URL
        notebook_file = Path(notebook_path)
        if notebook_file.is_absolute():
            # Get relative path from the notebook directory
            try:
                relative_path = notebook_file.name  # Just use the filename
            except Exception:
                relative_path = notebook_path
        else:
            relative_path = notebook_path

        logger.info(f"Using relative path for websocket: {relative_path}")

        # Try to use the collaboration API first, fall back to manual websocket URL creation
        websocket_url = None
        try:
            websocket_url = get_jupyter_notebook_websocket_url(
                server_url=server_url, token=token, path=relative_path
            )
            logger.info("Successfully created websocket URL using collaboration API")
        except Exception as e:
            logger.warning(f"Collaboration API failed: {e}")
            # Fall back to manual websocket URL creation
            # Format: ws://localhost:port/api/collaboration/room/notebook_path?token=token
            ws_server_url = server_url.replace("http://", "ws://").replace(
                "https://", "wss://"
            )
            websocket_url = (
                f"{ws_server_url}/api/collaboration/room/{relative_path}?token={token}"
            )
            logger.info(f"Using fallback websocket URL: {websocket_url}")

        async def amain():
            global kernel
            global notebook

            logger.info("Starting the kernel client...")
            with KernelClient(server_url=server_url, token=token) as kernel:
                logger.info("Kernel client connected successfully")
                async with NbModelClient(websocket_url) as notebook:
                    logger.info("Notebook client connected successfully")
                    for cell_type in ("code", "markdown", "raw"):
                        add_name = f"add_{cell_type}_cell"
                        add_func = getattr(notebook, add_name)
                        insert_name = f"insert_{cell_type}_cell"
                        insert_func = getattr(notebook, insert_name, None)

                        def add_cell(source: str, kwargs: dict | None = None) -> int:
                            return add_func(source, **(kwargs or {}))

                        def insert_cell(
                            index: int, source: str, kwargs: dict | None = None
                        ):
                            insert_func(index, source, **(kwargs or {}))
                            return f"{cell_type.capitalize()} cell inserted."

                        mcp.tool(f"add_{cell_type}_cell", add_func.__doc__)(add_cell)
                        if insert_func is not None:
                            mcp.tool(f"insert_{cell_type}_cell", insert_func.__doc__)(
                                insert_cell
                            )
                    mcp.tool()(notebook.get_cell_metadata)
                    mcp.tool()(notebook.get_cell_source)
                    mcp.tool()(notebook.get_notebook_metadata)
                    mcp.tool()(notebook.set_cell_metadata)
                    mcp.tool()(notebook.set_cell_source)
                    mcp.tool()(notebook.set_notebook_metadata)
                    logger.info("MCP tools registered, starting MCP server...")
                    await mcp.run_stdio_async()

        asyncio.run(amain())
    finally:
        # Restore original proxy settings
        os.environ.clear()
        os.environ.update(original_proxies)


if __name__ == "__main__":
    main()
